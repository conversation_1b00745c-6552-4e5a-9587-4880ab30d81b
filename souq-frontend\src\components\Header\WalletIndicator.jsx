import React, { useState, useEffect } from 'react';
import { Wallet, Eye, EyeOff } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { getWalletBalance, formatCurrency } from '../../api/WalletService';

const WalletIndicator = () => {
  const [balance, setBalance] = useState(0);
  const [currency, setCurrency] = useState('USD');
  const [showBalance, setShowBalance] = useState(false);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    loadBalance();
  }, []);

  const loadBalance = async () => {
    try {
      const response = await getWalletBalance('USD');
      if (response.success) {
        setBalance(response.data.balance);
        setCurrency(response.data.currency);
      }
    } catch (error) {
      console.error('Error loading wallet balance:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleWalletClick = () => {
    navigate('/wallet');
  };

  if (loading) {
    return (
      <div className="flex items-center space-x-2 px-3 py-1 bg-gray-100 rounded-lg animate-pulse">
        <Wallet className="w-4 h-4 text-gray-400" />
        <div className="w-12 h-4 bg-gray-300 rounded"></div>
      </div>
    );
  }

  return (
    <div 
      onClick={handleWalletClick}
      className="flex items-center space-x-2 px-3 py-1 bg-teal-50 hover:bg-teal-100 rounded-lg cursor-pointer transition-colors group"
    >
      <Wallet className="w-4 h-4 text-teal-600" />
      <div className="flex items-center space-x-1">
        <span className="text-sm font-medium text-teal-700">
          {showBalance ? formatCurrency(balance, currency) : '••••'}
        </span>
        <button
          onClick={(e) => {
            e.stopPropagation();
            setShowBalance(!showBalance);
          }}
          className="opacity-0 group-hover:opacity-100 transition-opacity"
        >
          {showBalance ? 
            <EyeOff className="w-3 h-3 text-teal-600" /> : 
            <Eye className="w-3 h-3 text-teal-600" />
          }
        </button>
      </div>
    </div>
  );
};

export default WalletIndicator;
