const Wallet = require('../../../../db/models/walletModel');
const { successResponse, errorResponse } = require('../../../../utils/responseHandler');
const currencyService = require('../../../../services/currency/CurrencyService');
// const PaymentCompletionService = require('../../../../services/payment/PaymentCompletionService'); // Removed to avoid circular dependency
const { findEscrowTransaction, findStandardPayment, findTransaction } = require('../../../../utils/transactionUtils');
const { creditWalletInternal } = require('../../../../utils/walletUtils');

/**
 * Comprehensive transaction finder that searches across all possible locations
 */
const findTransactionComprehensive = async (identifier) => {
  try {
    console.log(`🔍 Comprehensive search for identifier: ${identifier}`);

    // Import models
    const Order = require('../../../../db/models/orderModel');
    const EscrowTransaction = require('../../../../db/models/escrowTransactionModel');

    // Method 1: Direct search in escrow transactions using utility function
    console.log('🔍 Method 1: Direct escrow transaction search');
    let escrowTransaction = await findEscrowTransaction(identifier);

    if (escrowTransaction) {
      console.log(`✅ Found escrow transaction directly: ${escrowTransaction._id}`);
      return {
        transaction: escrowTransaction,
        type: 'escrow',
        source: 'escrowtransactions_direct'
      };
    }

    // Method 2: Find by order and then find corresponding escrow transaction
    console.log('🔍 Method 2: Find order and corresponding escrow transaction');
    let order = null;

    // Try to find order by various identifiers
    if (identifier.length === 24) { // Looks like ObjectId
      order = await Order.findById(identifier).populate('buyer seller product');
    }

    if (!order) {
      order = await Order.findOne({
        $or: [
          { orderNumber: identifier },
          { 'payment.transactionId': identifier }
        ]
      }).populate('buyer seller product');
    }

    if (order && order.type === 'escrow') {
      console.log(`✅ Found escrow order: ${order._id}, orderNumber: ${order.orderNumber}`);

      // Now find the corresponding escrow transaction
      escrowTransaction = await EscrowTransaction.findOne({
        $or: [
          { transactionId: order.orderNumber },
          { transactionId: order.payment?.transactionId },
          { gatewayTransactionId: order.payment?.transactionId },
          // Try to match by buyer, seller, and product
          {
            buyer: order.buyer._id,
            seller: order.seller._id,
            product: order.product._id
          }
        ]
      }).populate('buyer seller product');

      if (escrowTransaction) {
        console.log(`✅ Found corresponding escrow transaction: ${escrowTransaction._id}`);
        return {
          transaction: escrowTransaction,
          type: 'escrow',
          source: 'escrowtransactions_via_order'
        };
      } else {
        console.log(`⚠️ Order found but no corresponding escrow transaction`);
        console.log(`⚠️ Order details: buyer=${order.buyer._id}, seller=${order.seller._id}, product=${order.product._id}`);

        // Create a virtual escrow transaction from order data for processing
        const virtualEscrowTransaction = {
          _id: order._id,
          transactionId: order.orderNumber,
          gatewayTransactionId: order.payment?.transactionId,
          buyer: order.buyer,
          seller: order.seller,
          product: order.product,
          productPrice: order.orderDetails?.productPrice || order.product?.price,
          currency: order.orderDetails?.currency || 'USD',
          status: order.payment?.status === 'completed' ? 'completed' : 'payment_processing',
          createdAt: order.createdAt,
          updatedAt: order.updatedAt
        };

        console.log(`✅ Created virtual escrow transaction from order data`);
        return {
          transaction: virtualEscrowTransaction,
          type: 'escrow',
          source: 'virtual_from_order'
        };
      }
    }

    // Method 3: Search in standard payments using utility function
    console.log('🔍 Method 3: Standard payment search');
    const standardPayment = await findStandardPayment(identifier);

    if (standardPayment) {
      console.log(`✅ Found standard payment: ${standardPayment._id}`);
      return {
        transaction: standardPayment,
        type: 'standard',
        source: 'standardpayments'
      };
    }

    // Method 4: Search in main transactions table using utility function
    console.log('🔍 Method 4: Main transactions table search');
    const mainTransaction = await findTransaction(identifier);

    if (mainTransaction) {
      console.log(`✅ Found main transaction: ${mainTransaction._id}`);
      const type = mainTransaction.escrowTransaction ? 'escrow' : 'standard';
      return {
        transaction: mainTransaction,
        type: type,
        source: 'transactions'
      };
    }

    console.log(`❌ Transaction not found with identifier: ${identifier}`);
    return null;

  } catch (error) {
    console.error('❌ Error in comprehensive transaction search:', error);
    throw error;
  }
};

/**
 * Get user's wallet details
 */
exports.getWallet = async (req, res) => {
  try {
    console.log('💰 Getting wallet for user:', req.user._id);
    const userId = req.user._id;

    if (!userId) {
      console.error('❌ No user ID provided');
      return errorResponse(res, 'User ID is required', 400);
    }

    console.log('🔍 Finding or creating wallet for user:', userId);
    const wallet = await Wallet.findOrCreateWallet(userId);
    console.log('✅ Wallet found/created:', wallet._id);

    const walletData = {
      id: wallet._id,
      balances: wallet.balances,
      primaryCurrency: wallet.primaryCurrency,
      totalBalance: wallet.totalBalance,
      isActive: wallet.isActive,
      isBlocked: wallet.isBlocked,
      withdrawalLimit: wallet.withdrawalLimit,
      statistics: wallet.statistics,
      createdAt: wallet.createdAt,
      updatedAt: wallet.updatedAt
    };

    console.log('📊 Returning wallet data:', walletData);

    return successResponse(res, 'Wallet retrieved successfully', {
      wallet: walletData
    });

  } catch (error) {
    console.error('❌ Get wallet error:', error);
    console.error('Error stack:', error.stack);
    return errorResponse(res, 'Failed to retrieve wallet: ' + error.message, 500);
  }
};

/**
 * Get wallet transaction history
 */
exports.getTransactionHistory = async (req, res) => {
  try {
    console.log('📋 Getting transaction history for user:', req.user._id);
    const userId = req.user._id;
    const { page = 1, limit = 20, type, currency } = req.query;

    const wallet = await Wallet.findOne({ user: userId })
      .populate('user', 'firstName lastName email')
      .populate('transactions.relatedProduct', 'title price product_photos')
      .populate({
        path: 'transactions.relatedTransaction',
        populate: {
          path: 'buyer seller product',
          select: 'firstName lastName email title price product_photos'
        }
      })
      .populate({
        path: 'transactions.relatedEscrowTransaction',
        populate: {
          path: 'buyer seller product',
          select: 'firstName lastName email title price product_photos'
        }
      });

    if (!wallet) {
      console.log('⚠️ Wallet not found for user:', userId);
      // Create wallet if it doesn't exist
      const newWallet = await Wallet.findOrCreateWallet(userId);
      console.log('✅ Created new wallet:', newWallet._id);

      return successResponse(res, 'Transaction history retrieved successfully', {
        transactions: [],
        pagination: {
          currentPage: parseInt(page),
          totalPages: 0,
          totalTransactions: 0,
          hasNext: false,
          hasPrev: false
        }
      });
    }

    let transactions = wallet.transactions || [];
    console.log(`📊 Found ${transactions.length} transactions`);

    // Filter by type if specified
    if (type) {
      transactions = transactions.filter(tx => tx.type === type);
      console.log(`🔍 Filtered by type '${type}': ${transactions.length} transactions`);
    }

    // Filter by currency if specified
    if (currency) {
      transactions = transactions.filter(tx => tx.currency === currency);
      console.log(`🔍 Filtered by currency '${currency}': ${transactions.length} transactions`);
    }

    // Pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedTransactions = transactions.slice(startIndex, endIndex);

    console.log(`📄 Returning page ${page} with ${paginatedTransactions.length} transactions`);

    return successResponse(res, 'Transaction history retrieved successfully', {
      transactions: paginatedTransactions,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(transactions.length / limit),
        totalTransactions: transactions.length,
        hasNext: endIndex < transactions.length,
        hasPrev: startIndex > 0
      }
    });

  } catch (error) {
    console.error('❌ Get transaction history error:', error);
    console.error('Error stack:', error.stack);
    return errorResponse(res, 'Failed to retrieve transaction history: ' + error.message, 500);
  }
};

/**
 * Withdraw money from wallet
 */
exports.withdrawMoney = async (req, res) => {
  try {
    const userId = req.user._id;
    const { amount, currency = 'USD', withdrawalMethod, bankAccountId, description } = req.body;
    
    // Validation
    if (!amount || amount <= 0) {
      return errorResponse(res, 'Invalid withdrawal amount', 400);
    }
    
    if (!withdrawalMethod || !['bank_transfer', 'paypal'].includes(withdrawalMethod)) {
      return errorResponse(res, 'Invalid withdrawal method', 400);
    }
    
    const wallet = await Wallet.findOne({ user: userId });
    
    if (!wallet) {
      return errorResponse(res, 'Wallet not found', 404);
    }
    
    // Check if wallet is blocked
    if (wallet.isBlocked) {
      return errorResponse(res, 'Wallet is blocked. Please contact support.', 403);
    }
    
    // Check withdrawal eligibility
    const canWithdraw = wallet.canWithdraw(amount, currency);
    if (!canWithdraw.canWithdraw) {
      return errorResponse(res, canWithdraw.reason, 400);
    }
    
    // Process withdrawal
    await wallet.addTransaction({
      type: 'withdrawal',
      amount,
      currency,
      description: description || `Withdrawal via ${withdrawalMethod}`,
      metadata: {
        withdrawalMethod,
        bankAccountId,
        status: 'pending'
      }
    });
    
    // Update withdrawal tracking
    wallet.withdrawalTracking.dailyWithdrawn += amount;
    wallet.withdrawalTracking.monthlyWithdrawn += amount;
    await wallet.save();
    
    // TODO: Integrate with payment gateway for actual withdrawal processing
    
    return successResponse(res, 'Withdrawal request submitted successfully', {
      transactionId: wallet.transactions[0].transactionId,
      amount,
      currency,
      status: 'pending',
      estimatedProcessingTime: '1-3 business days'
    });
    
  } catch (error) {
    console.error('Withdraw money error:', error);
    return errorResponse(res, error.message || 'Failed to process withdrawal', 500);
  }
};

/**
 * Get wallet balance in specific currency
 */
exports.getBalance = async (req, res) => {
  try {
    const userId = req.user._id;
    const { currency = 'USD' } = req.query;
    
    const wallet = await Wallet.findOrCreateWallet(userId);
    
    const balance = wallet.balances[currency] || 0;
    
    return successResponse(res, 'Balance retrieved successfully', {
      balance,
      currency,
      formattedBalance: `${currency} ${balance.toFixed(2)}`
    });
    
  } catch (error) {
    console.error('Get balance error:', error);
    return errorResponse(res, 'Failed to retrieve balance', 500);
  }
};

/**
 * Update wallet settings
 */
exports.updateWalletSettings = async (req, res) => {
  try {
    const userId = req.user._id;
    const { primaryCurrency, withdrawalLimit } = req.body;
    
    const wallet = await Wallet.findOne({ user: userId });
    
    if (!wallet) {
      return errorResponse(res, 'Wallet not found', 404);
    }
    
    // Update primary currency if provided
    if (primaryCurrency && ['USD', 'AED', 'EUR', 'GBP'].includes(primaryCurrency)) {
      wallet.primaryCurrency = primaryCurrency;
    }
    
    // Update withdrawal limits if provided
    if (withdrawalLimit) {
      if (withdrawalLimit.daily && withdrawalLimit.daily > 0) {
        wallet.withdrawalLimit.daily = withdrawalLimit.daily;
      }
      if (withdrawalLimit.monthly && withdrawalLimit.monthly > 0) {
        wallet.withdrawalLimit.monthly = withdrawalLimit.monthly;
      }
    }
    
    await wallet.save();
    
    return successResponse(res, 'Wallet settings updated successfully', {
      primaryCurrency: wallet.primaryCurrency,
      withdrawalLimit: wallet.withdrawalLimit
    });
    
  } catch (error) {
    console.error('Update wallet settings error:', error);
    return errorResponse(res, 'Failed to update wallet settings', 500);
  }
};

/**
 * Credit wallet (internal use - called when seller receives payment)
 */
exports.creditWalletInternal = require('../../../../utils/walletUtils').creditWalletExternal;









/**
 * Complete payment and credit wallet
 */
exports.completePayment = async (req, res) => {
  try {
    console.log('💰 Complete payment and credit wallet request - START');
    console.log('📋 Request body:', JSON.stringify(req.body, null, 2));
    console.log('👤 User:', req.user ? req.user._id : 'No user');

    // Validate request
    if (!req.user || !req.user._id) {
      console.error('❌ No authenticated user found');
      return errorResponse(res, 'Authentication required', 401);
    }

    let { transactionId, transactionType = 'standard' } = req.body;

    if (!transactionId) {
      console.error('❌ Missing transaction ID in request');
      return errorResponse(res, 'Transaction ID is required', 400);
    }

    console.log('✅ Initial validation passed');

    console.log('🔍 Transaction details:');
    console.log(`🔍   - Transaction ID: ${transactionId}`);
    console.log(`🔍   - Transaction Type: ${transactionType}`);
    console.log(`🔍   - ID Length: ${transactionId.length}`);
    console.log(`🔍   - ID Pattern: ${transactionId.startsWith('TXN_') ? 'Internal TXN_' : transactionId.startsWith('pi_') ? 'Stripe PI_' : transactionId.startsWith('ESC-') ? 'Escrow ESC-' : 'Unknown'}`);
    console.log(`🔍   - User ID: ${req.user._id}`);
    console.log('🔍 ==========================================');

    // Auto-detect transaction type and find the transaction
    if (!transactionType || transactionType === 'auto') {
      console.log('🔍 Auto-detecting transaction type...');
      console.log(`🔍 Searching for transaction with ID: ${transactionId}`);

      try {
        // Try to find the transaction using comprehensive search
        const foundTransaction = await findTransactionComprehensive(transactionId);
        console.log('🔍 Found my transaction:======', foundTransaction);

        if (foundTransaction) {
          transactionType = foundTransaction.type;
          console.log(`✅ Found transaction: ${foundTransaction.transaction._id}`);
          console.log(`✅ Transaction type: ${foundTransaction.type}`);
          console.log(`✅ Found in: ${foundTransaction.source}`);
        } else {
          console.error('❌ Transaction not found in any table');
          console.error(`❌ Searched for transaction ID: ${transactionId}`);
          console.error('❌ Check if the transaction ID is correct');
          return errorResponse(res, `Transaction not found: ${transactionId}`, 404);
        }
      } catch (searchError) {
        console.error('❌ Error during transaction search:', searchError);
        return errorResponse(res, `Error searching for transaction: ${searchError.message}`, 500);
      }
    }

    console.log(`💰 Completing ${transactionType} payment: ${transactionId}`);

    // Find the actual transaction using our comprehensive finder
    let foundTransaction;
    try {
      console.log('🔍 Starting comprehensive transaction search...');
      foundTransaction = await findTransactionComprehensive(transactionId);
      console.log('🔍 Comprehensive search completed');
    } catch (findError) {
      console.error('❌ Error during comprehensive transaction search:', findError);
      return errorResponse(res, `Error finding transaction: ${findError.message}`, 500);
    }

    if (!foundTransaction) {
      console.error('❌ Transaction not found by comprehensive search');
      return errorResponse(res, `Transaction not found: ${transactionId}`, 404);
    }

    console.log(`✅ Found transaction via ${foundTransaction.source}`);
    console.log(`✅ Transaction type: ${foundTransaction.type}`);
    console.log(`✅ Transaction ID: ${foundTransaction.transaction._id}`);

    let result;

    if (foundTransaction.source === 'virtual_from_order') {
      // For virtual transactions, handle directly without PaymentCompletionService
      console.log('🔄 Processing virtual escrow transaction from order data');

      const orderData = foundTransaction.transaction;

      // Calculate seller amount (product price minus platform fee)
      const productPrice = orderData.productPrice || 0;
      const platformFeeAmount = productPrice * 0.1; // 10% platform fee
      const sellerAmount = productPrice - platformFeeAmount;

      // Credit the seller's wallet directly
      console.log('🔄 Calling creditWalletInternal with:', {
        sellerId: orderData.seller._id,
        sellerAmount,
        currency: orderData.currency,
        transactionId: orderData.transactionId
      });

      let creditResult;
      try {
        creditResult = await creditWalletInternal(
          orderData.seller._id,
          sellerAmount,
          orderData.currency,
          {
            type: 'sale',
            description: `Sale of ${orderData.product?.title || 'Product'}`,
            transactionId: orderData.transactionId,
            transactionType: 'escrow'
          }
        );
      } catch (creditError) {
        console.error('❌ Error calling creditWalletInternal:', creditError);
        return errorResponse(res, `Failed to credit wallet: ${creditError.message}`, 500);
      }

      console.log('🔄 creditWalletInternal result:', creditResult);

      if (creditResult && creditResult.success) {
        result = {
          success: true,
          walletCredited: true,
          sellerAmount,
          currency: orderData.currency,
          newBalance: creditResult.newBalance,
          productTitle: orderData.product?.title
        };
      } else {
        const errorMessage = creditResult?.error || 'Failed to credit wallet - creditResult is undefined or failed';
        console.error('❌ Failed to credit wallet for virtual transaction:', errorMessage);
        console.error('❌ creditResult details:', creditResult);
        return errorResponse(res, errorMessage, 500);
      }
    } else {
      // For real transactions, handle payment completion directly
      const actualTransactionId = foundTransaction.transaction._id;
      const transaction = foundTransaction.transaction;

      console.log(`🔄 Processing real transaction with ID: ${actualTransactionId}`);
      console.log(`🔄 Transaction type: ${foundTransaction.type}`);

      if (foundTransaction.type === 'escrow') {
        // Handle escrow transaction completion
        console.log('🛡️ Processing escrow transaction completion');

        // Check if already completed
        if (transaction.status === 'completed') {
          console.log('⚠️ Escrow already completed');
          result = { success: true, alreadyCompleted: true };
        } else {
          // Credit seller's wallet
          const sellerAmount = transaction.productPrice - (transaction.platformFeeAmount || 0);

          if (sellerAmount > 0) {
            const walletResult = await creditWalletInternal(
              transaction.seller._id,
              sellerAmount,
              transaction.currency,
              {
                type: 'sale',
                description: `Escrow payment for product: ${transaction.product?.title || 'Product'}`,
                transactionId: transaction.transactionId,
                transactionType: 'escrow'
              }
            );

            if (walletResult.success) {
              console.log(`✅ Escrow completed and wallet credited: ${transaction.currency} ${sellerAmount}`);
              result = {
                success: true,
                walletCredited: true,
                sellerAmount,
                currency: transaction.currency
              };
            } else {
              console.error('❌ Failed to credit wallet for escrow:', walletResult.error);
              result = {
                success: false,
                error: 'Failed to credit seller wallet'
              };
            }
          } else {
            result = {
              success: true,
              walletCredited: false,
              message: 'Escrow completed but no wallet credit needed'
            };
          }
        }
      } else if (foundTransaction.type === 'standard') {
        // Handle standard payment completion
        console.log('💳 Processing standard payment completion');

        // Check if already completed
        if (transaction.status === 'completed') {
          console.log('⚠️ Standard payment already completed');
          result = { success: true, alreadyCompleted: true };
        } else {
          // Credit seller's wallet
          const sellerAmount = transaction.productPrice - (transaction.platformFeeAmount || 0);

          if (sellerAmount > 0) {
            const walletResult = await creditWalletInternal(
              transaction.seller._id,
              sellerAmount,
              transaction.currency,
              {
                type: 'sale',
                description: `Standard payment for product: ${transaction.product?.title || 'Product'}`,
                transactionId: transaction.transactionId,
                transactionType: 'standard'
              }
            );

            if (walletResult.success) {
              console.log(`✅ Standard payment completed and wallet credited: ${transaction.currency} ${sellerAmount}`);
              result = {
                success: true,
                walletCredited: true,
                sellerAmount,
                currency: transaction.currency
              };
            } else {
              console.error('❌ Failed to credit wallet for standard payment:', walletResult.error);
              result = {
                success: false,
                error: 'Failed to credit seller wallet'
              };
            }
          } else {
            result = {
              success: true,
              walletCredited: false,
              message: 'Standard payment completed but no wallet credit needed'
            };
          }
        }
      } else {
        console.error('❌ Unknown transaction type:', foundTransaction.type);
        result = {
          success: false,
          error: `Unknown transaction type: ${foundTransaction.type}`
        };
      }
    }

    console.log('📊 Payment completion result:', JSON.stringify(result, null, 2));

    if (result.success) {
      if (result.alreadyCompleted) {
        console.log('ℹ️ Payment was already completed');
        return successResponse(res, 'Payment was already completed', {
          transactionId,
          transactionType,
          alreadyCompleted: true
        });
      }

      console.log('✅ Payment completion successful');
      return successResponse(res, 'Payment completed and wallet credited successfully', {
        transactionId,
        transactionType,
        walletCredited: result.walletCredited,
        sellerAmount: result.sellerAmount,
        currency: result.currency
      });
    } else {
      console.error('❌ Payment completion failed:', result.error);
      return errorResponse(res, result.error || 'Failed to complete payment', 500);
    }

  } catch (error) {
    console.error('❌ Complete payment error:', error);
    console.error('❌ Error stack:', error.stack);
    console.error('❌ Error details:', {
      message: error.message,
      name: error.name,
      code: error.code
    });
    return errorResponse(res, 'Failed to complete payment: ' + error.message, 500);
  }
};

/**
 * Credit wallet based on transaction completion
 */
exports.creditFromTransaction = async (req, res) => {
  try {
    console.log('💰 Credit wallet from transaction request');
    const { transactionId, transactionType = 'standard' } = req.body;

    if (!transactionId) {
      return errorResponse(res, 'Transaction ID is required', 400);
    }

    console.log(`💰 Processing wallet credit for ${transactionType} transaction: ${transactionId}`);

    let transaction;
    let sellerAmount;
    let currency;
    let productTitle;
    let sellerId;

    if (transactionType === 'standard') {
      // Get standard payment details using utility function
      transaction = await findStandardPayment(transactionId, true);

      if (!transaction) {
        return errorResponse(res, 'Standard payment transaction not found', 404);
      }

      sellerId = transaction.seller._id;
      sellerAmount = transaction.productPrice - (transaction.platformFeeAmount || 0);
      currency = transaction.currency;
      productTitle = transaction.product?.title || 'Product';

    } else if (transactionType === 'escrow') {
      // Get escrow transaction details using utility function
      transaction = await findEscrowTransaction(transactionId, true);

      if (!transaction) {
        return errorResponse(res, 'Escrow transaction not found', 404);
      }

      sellerId = transaction.seller._id;
      sellerAmount = transaction.productPrice - (transaction.platformFeeAmount || 0);
      currency = transaction.currency;
      productTitle = transaction.product?.title || 'Product';
    } else {
      return errorResponse(res, 'Invalid transaction type', 400);
    }

    // Check if wallet has already been credited for this transaction
    const Wallet = require('../../../../db/models/walletModel');
    const existingWallet = await Wallet.findOne({ user: sellerId });

    if (existingWallet) {
      const existingTransaction = existingWallet.transactions.find(t =>
        t.relatedTransaction && t.relatedTransaction.toString() === transaction._id.toString()
      );

      if (existingTransaction) {
        console.log('⚠️ Wallet already credited for this transaction');
        return successResponse(res, 'Wallet already credited for this transaction', {
          alreadyCredited: true,
          existingAmount: existingTransaction.amount,
          currency: existingTransaction.currency
        });
      }
    }

    if (sellerAmount > 0) {
      const walletResult = await creditWalletInternal(
        sellerId,
        sellerAmount,
        currency,
        `Payment for product: ${productTitle}`,
        {
          relatedTransaction: transaction._id,
          relatedProduct: transaction.product,
          metadata: {
            transactionId: transaction.transactionId,
            transactionType: transactionType,
            originalAmount: transaction.productPrice,
            platformFee: transaction.platformFeeAmount,
            netAmount: sellerAmount,
            timestamp: new Date().toISOString()
          }
        }
      );

      if (walletResult.success) {
        console.log(`✅ Wallet credited from ${transactionType} transaction: ${currency} ${sellerAmount}`);
        return successResponse(res, 'Wallet credited successfully', {
          transactionId,
          transactionType,
          sellerAmount,
          currency,
          newBalance: walletResult.newBalance,
          productTitle
        });
      } else {
        console.error('❌ Failed to credit wallet:', walletResult.error);
        return errorResponse(res, walletResult.error || 'Failed to credit wallet', 500);
      }
    } else {
      return errorResponse(res, 'Invalid seller amount', 400);
    }

  } catch (error) {
    console.error('❌ Credit from transaction error:', error);
    return errorResponse(res, 'Failed to credit wallet: ' + error.message, 500);
  }
};

/**
 * Simulate product purchase and wallet credit (for testing)
 */
exports.simulatePurchase = async (req, res) => {
  try {
    console.log('🛒 Simulating product purchase and wallet credit');
    const sellerId = req.user._id;
    const { productPrice = 100, currency = 'USD', productTitle = 'Test Product' } = req.body;

    // Calculate platform fee (10%)
    const platformFee = productPrice * 0.1;
    const sellerAmount = productPrice - platformFee;

    console.log(`🛒 Simulating purchase: Product Price: ${currency} ${productPrice}, Platform Fee: ${currency} ${platformFee}, Seller Gets: ${currency} ${sellerAmount}`);

    const walletResult = await creditWalletInternal(
      sellerId,
      sellerAmount,
      currency,
      `Sale of product: ${productTitle}`,
      {
        metadata: {
          type: 'product_sale',
          originalAmount: productPrice,
          platformFee: platformFee,
          netAmount: sellerAmount,
          timestamp: new Date().toISOString()
        }
      }
    );

    if (walletResult.success) {
      console.log(`✅ Purchase simulation successful. Seller earned: ${currency} ${sellerAmount}`);
      return successResponse(res, 'Purchase simulated successfully', {
        productPrice,
        platformFee,
        sellerAmount,
        currency,
        newBalance: walletResult.newBalance,
        productTitle
      });
    } else {
      console.error('❌ Purchase simulation failed:', walletResult.error);
      return errorResponse(res, walletResult.error || 'Failed to simulate purchase', 500);
    }

  } catch (error) {
    console.error('❌ Simulate purchase error:', error);
    return errorResponse(res, 'Failed to simulate purchase: ' + error.message, 500);
  }
};

/**
 * Get comprehensive transaction data for wallet
 */
exports.getComprehensiveTransactionData = async (req, res) => {
  try {
    console.log('📊 Getting comprehensive transaction data for user:', req.user._id);
    const userId = req.user._id;

    // Get wallet with populated transactions
    const wallet = await Wallet.findOne({ user: userId })
      .populate('user', 'firstName lastName email')
      .populate('transactions.relatedProduct', 'title price product_photos')
      .populate({
        path: 'transactions.relatedTransaction',
        populate: {
          path: 'buyer seller product',
          select: 'firstName lastName email title price product_photos'
        }
      })
      .populate({
        path: 'transactions.relatedEscrowTransaction',
        populate: {
          path: 'buyer seller product',
          select: 'firstName lastName email title price product_photos'
        }
      });

    // Get all standard payments involving this user
    const StandardPayment = require('../../../../db/models/standardPaymentModel');
    const standardPayments = await StandardPayment.find({
      $or: [
        { buyer: userId },
        { seller: userId }
      ]
    })
    .populate('buyer', 'firstName lastName email')
    .populate('seller', 'firstName lastName email')
    .populate('product', 'title price product_photos')
    .sort({ createdAt: -1 });

    // Get all escrow transactions involving this user
    const EscrowTransaction = require('../../../../db/models/escrowTransactionModel');
    const escrowTransactions = await EscrowTransaction.find({
      $or: [
        { buyer: userId },
        { seller: userId }
      ]
    })
    .populate('buyer', 'firstName lastName email')
    .populate('seller', 'firstName lastName email')
    .populate('product', 'title price product_photos')
    .sort({ createdAt: -1 });

    // Format transaction data
    const formattedData = {
      wallet: {
        exists: !!wallet,
        balances: wallet?.balances || {},
        totalTransactions: wallet?.transactions?.length || 0,
        transactions: wallet?.transactions?.map(tx => ({
          id: tx._id,
          type: tx.type,
          amount: tx.amount,
          currency: tx.currency,
          description: tx.description,
          status: tx.status,
          createdAt: tx.createdAt,
          buyer: tx.metadata?.buyerName || 'Unknown',
          buyerEmail: tx.metadata?.buyerEmail || '',
          product: tx.relatedProduct?.title || 'Unknown Product',
          productPrice: tx.relatedProduct?.price || 0,
          originalAmount: tx.metadata?.originalAmount || tx.amount,
          platformFee: tx.metadata?.platformFee || 0,
          netAmount: tx.metadata?.netAmount || tx.amount,
          paymentType: tx.metadata?.paymentType || 'unknown'
        })) || []
      },
      standardPayments: standardPayments.map(payment => ({
        id: payment._id,
        transactionId: payment.transactionId,
        status: payment.status,
        amount: payment.totalAmount,
        productPrice: payment.productPrice,
        platformFee: payment.platformFeeAmount,
        currency: payment.currency,
        buyer: {
          id: payment.buyer?._id,
          name: `${payment.buyer?.firstName} ${payment.buyer?.lastName}`,
          email: payment.buyer?.email
        },
        seller: {
          id: payment.seller?._id,
          name: `${payment.seller?.firstName} ${payment.seller?.lastName}`,
          email: payment.seller?.email
        },
        product: {
          id: payment.product?._id,
          title: payment.product?.title,
          price: payment.product?.price,
          image: payment.product?.product_photos?.[0]
        },
        createdAt: payment.createdAt,
        completedAt: payment.completedAt
      })),
      escrowTransactions: escrowTransactions.map(escrow => ({
        id: escrow._id,
        transactionId: escrow.transactionId,
        status: escrow.status,
        amount: escrow.totalAmount,
        productPrice: escrow.productPrice,
        platformFee: escrow.platformFeeAmount,
        currency: escrow.currency,
        buyer: {
          id: escrow.buyer?._id,
          name: `${escrow.buyer?.firstName} ${escrow.buyer?.lastName}`,
          email: escrow.buyer?.email
        },
        seller: {
          id: escrow.seller?._id,
          name: `${escrow.seller?.firstName} ${escrow.seller?.lastName}`,
          email: escrow.seller?.email
        },
        product: {
          id: escrow.product?._id,
          title: escrow.product?.title,
          price: escrow.product?.price,
          image: escrow.product?.product_photos?.[0]
        },
        createdAt: escrow.createdAt,
        completedAt: escrow.completedAt
      }))
    };

    console.log(`📊 Comprehensive data: ${formattedData.wallet.totalTransactions} wallet transactions, ${formattedData.standardPayments.length} standard payments, ${formattedData.escrowTransactions.length} escrow transactions`);

    return successResponse(res, 'Comprehensive transaction data retrieved successfully', formattedData);

  } catch (error) {
    console.error('❌ Get comprehensive transaction data error:', error);
    return errorResponse(res, 'Failed to get transaction data: ' + error.message, 500);
  }
};

/**
 * Debug payment status and wallet integration
 */
exports.debugPaymentStatus = async (req, res) => {
  try {
    console.log('🔍 Debug payment status request');
    const userId = req.user._id;

    // Get recent standard payments
    const StandardPayment = require('../../../../db/models/standardPaymentModel');
    const recentStandardPayments = await StandardPayment.find({
      $or: [
        { buyer: userId },
        { seller: userId }
      ]
    })
    .populate('buyer', 'firstName lastName email')
    .populate('seller', 'firstName lastName email')
    .populate('product', 'title price')
    .sort({ createdAt: -1 })
    .limit(5);

    // Get recent escrow transactions
    const EscrowTransaction = require('../../../../db/models/escrowTransactionModel');
    const recentEscrowTransactions = await EscrowTransaction.find({
      $or: [
        { buyer: userId },
        { seller: userId }
      ]
    })
    .populate('buyer', 'firstName lastName email')
    .populate('seller', 'firstName lastName email')
    .populate('product', 'title price')
    .sort({ createdAt: -1 })
    .limit(5);

    // Get wallet info
    const Wallet = require('../../../../db/models/walletModel');
    const wallet = await Wallet.findOne({ user: userId });

    const debugInfo = {
      userId,
      wallet: {
        exists: !!wallet,
        balances: wallet?.balances || {},
        transactionCount: wallet?.transactions?.length || 0,
        recentTransactions: wallet?.transactions?.slice(-3) || []
      },
      recentPayments: {
        standard: recentStandardPayments.map(p => ({
          id: p._id,
          transactionId: p.transactionId,
          status: p.status,
          amount: p.totalAmount,
          currency: p.currency,
          buyer: p.buyer?.firstName + ' ' + p.buyer?.lastName,
          seller: p.seller?.firstName + ' ' + p.seller?.lastName,
          product: p.product?.title,
          createdAt: p.createdAt
        })),
        escrow: recentEscrowTransactions.map(e => ({
          id: e._id,
          transactionId: e.transactionId,
          status: e.status,
          amount: e.totalAmount,
          currency: e.currency,
          buyer: e.buyer?.firstName + ' ' + e.buyer?.lastName,
          seller: e.seller?.firstName + ' ' + e.seller?.lastName,
          product: e.product?.title,
          createdAt: e.createdAt
        }))
      }
    };

    console.log('🔍 Debug info:', debugInfo);

    return successResponse(res, 'Debug information retrieved', debugInfo);

  } catch (error) {
    console.error('❌ Debug payment status error:', error);
    return errorResponse(res, 'Failed to get debug info: ' + error.message, 500);
  }
};

/**
 * Manual credit wallet (for testing)
 */
exports.manualCreditWallet = async (req, res) => {
  try {
    console.log('💰 Manual wallet credit request');
    const userId = req.user._id;
    const { amount, currency = 'USD', description = 'Manual credit for testing' } = req.body;

    if (!amount || amount <= 0) {
      return errorResponse(res, 'Invalid amount', 400);
    }

    console.log(`💰 Crediting ${currency} ${amount} to user ${userId}`);

    const walletResult = await creditWalletInternal(
      userId,
      parseFloat(amount),
      currency,
      description,
      {
        metadata: {
          type: 'manual_credit',
          timestamp: new Date().toISOString()
        }
      }
    );

    if (walletResult.success) {
      console.log(`✅ Manual credit successful. New balance: ${currency} ${walletResult.newBalance}`);
      return successResponse(res, 'Wallet credited successfully', {
        amount: parseFloat(amount),
        currency,
        newBalance: walletResult.newBalance,
        description
      });
    } else {
      console.error('❌ Manual credit failed:', walletResult.error);
      return errorResponse(res, walletResult.error || 'Failed to credit wallet', 500);
    }

  } catch (error) {
    console.error('❌ Manual credit wallet error:', error);
    return errorResponse(res, 'Failed to credit wallet: ' + error.message, 500);
  }
};

/**
 * Get wallet statistics
 */
exports.getWalletStatistics = async (req, res) => {
  try {
    console.log('📈 Getting wallet statistics for user:', req.user._id);
    const userId = req.user._id;
    const { period = '30' } = req.query; // days

    const wallet = await Wallet.findOne({ user: userId });

    if (!wallet) {
      console.log('⚠️ Wallet not found for statistics, creating new wallet');
      const newWallet = await Wallet.findOrCreateWallet(userId);

      // Return default statistics for new wallet
      return successResponse(res, 'Wallet statistics retrieved successfully', {
        totalBalance: 0,
        balances: newWallet.balances,
        period: {
          days: parseInt(period),
          totalTransactions: 0,
          totalEarned: 0,
          totalSpent: 0
        },
        overall: newWallet.statistics
      });
    }
    
    // Calculate statistics for the specified period
    const periodStart = new Date();
    periodStart.setDate(periodStart.getDate() - parseInt(period));
    
    const periodTransactions = wallet.transactions.filter(tx => 
      new Date(tx.createdAt) >= periodStart
    );
    
    const statistics = {
      totalBalance: wallet.totalBalance,
      balances: wallet.balances,
      period: {
        days: parseInt(period),
        totalTransactions: periodTransactions.length,
        totalEarned: periodTransactions
          .filter(tx => ['credit', 'refund', 'bonus'].includes(tx.type))
          .reduce((sum, tx) => sum + tx.amount, 0),
        totalSpent: periodTransactions
          .filter(tx => ['debit', 'withdrawal', 'fee'].includes(tx.type))
          .reduce((sum, tx) => sum + tx.amount, 0)
      },
      overall: wallet.statistics
    };
    
    return successResponse(res, 'Wallet statistics retrieved successfully', statistics);
    
  } catch (error) {
    console.error('Get wallet statistics error:', error);
    return errorResponse(res, 'Failed to retrieve wallet statistics', 500);
  }
};

/**
 * Test transaction lookup - for debugging
 */
exports.testTransactionLookup = async (req, res) => {
  try {
    const { transactionId, transactionType = 'escrow' } = req.query;

    if (!transactionId) {
      return errorResponse(res, 'Transaction ID is required', 400);
    }

    console.log(`🧪 Testing transaction lookup: ${transactionId} (type: ${transactionType})`);

    let transaction;
    if (transactionType === 'escrow') {
      transaction = await findEscrowTransaction(transactionId, true);
    } else {
      transaction = await findStandardPayment(transactionId, true);
    }

    if (transaction) {
      return successResponse(res, 'Transaction found', {
        found: true,
        transaction: {
          _id: transaction._id,
          transactionId: transaction.transactionId,
          status: transaction.status,
          productPrice: transaction.productPrice,
          seller: transaction.seller,
          buyer: transaction.buyer,
          product: transaction.product
        }
      });
    } else {
      return successResponse(res, 'Transaction not found', {
        found: false,
        searchedFor: transactionId,
        type: transactionType
      });
    }

  } catch (error) {
    console.error('❌ Error testing transaction lookup:', error);
    return errorResponse(res, 'Test failed: ' + error.message, 500);
  }
};
