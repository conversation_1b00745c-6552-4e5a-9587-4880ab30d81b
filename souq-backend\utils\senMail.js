const nodemailer = require('nodemailer');

// Check if email is disabled for development
const isEmailDisabled = process.env.DISABLE_EMAIL === 'true' || process.env.NODE_ENV === 'development';

let transporter = null;

// Only create transporter if email is enabled and credentials are available
if (!isEmailDisabled && process.env.EMAIL_USER && process.env.EMAIL_PASS) {
  try {
    transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      }
    });
    console.log('✅ Email transporter initialized successfully');
  } catch (error) {
    console.error('❌ Failed to initialize email transporter:', error.message);
  }
} else {
  console.log('⚠️ Email service disabled or credentials missing');
  console.log('   - EMAIL_USER:', process.env.EMAIL_USER ? 'Set' : 'Missing');
  console.log('   - EMAIL_PASS:', process.env.EMAIL_PASS ? 'Set' : 'Missing');
  console.log('   - DISABLE_EMAIL:', process.env.DISABLE_EMAIL);
  console.log('   - NODE_ENV:', process.env.NODE_ENV);
}

const sendMail = async (to, subject, html) => {
  try {
    console.log(`📧 Attempting to send email to: ${to}`);
    console.log(`📧 Subject: ${subject}`);

    // If email is disabled or no transporter, simulate sending
    if (isEmailDisabled || !transporter) {
      console.log('⚠️ Email sending is disabled - simulating email send');
      console.log('📧 Email content (would be sent):');
      console.log(`   To: ${to}`);
      console.log(`   Subject: ${subject}`);
      console.log(`   HTML: ${html}`);

      // Return a mock successful response
      return {
        messageId: 'mock-message-id',
        response: 'Email sending disabled - simulated success'
      };
    }

    const mailOptions = {
      from: process.env.EMAIL_USER,
      to,
      subject,
      html
    };

    console.log('📧 Sending email via Gmail...');
    const result = await transporter.sendMail(mailOptions);
    console.log('✅ Email sent successfully:', result.messageId);

    return result;

  } catch (err) {
    console.error("❌ Email sending failed:", err);
    console.error("❌ Error details:", {
      code: err.code,
      command: err.command,
      response: err.response,
      responseCode: err.responseCode
    });

    // Provide more specific error messages
    if (err.code === 'EAUTH') {
      throw new Error("Email authentication failed - check EMAIL_USER and EMAIL_PASS");
    } else if (err.code === 'ECONNECTION') {
      throw new Error("Email connection failed - check internet connection");
    } else if (err.code === 'EMESSAGE') {
      throw new Error("Email message format error");
    } else {
      throw new Error(`Email failed to send: ${err.message}`);
    }
  }
};

module.exports = sendMail;