const express = require('express');
const router = express.Router();
const verifyToken = require('../../../../utils/verifyToken');
const walletController = require('../controllers/walletController');

// All wallet routes require authentication
router.use(verifyToken);



// Get wallet details
router.get('/', walletController.getWallet);

// Get wallet balance for specific currency
router.get('/balance', walletController.getBalance);

// Get transaction history
router.get('/transactions', walletController.getTransactionHistory);

// Get wallet statistics
router.get('/statistics', walletController.getWalletStatistics);

// Get comprehensive transaction data
router.get('/comprehensive-data', walletController.getComprehensiveTransactionData);

// Withdraw money from wallet
router.post('/withdraw', walletController.withdrawMoney);

// Complete payment and credit wallet
router.post('/complete-payment', walletController.completePayment);

// Update wallet settings
router.put('/settings', walletController.updateWalletSettings);

// Fix wallet duplicate keys (Admin function)
router.post('/fix-duplicate-keys', walletController.fixWalletDuplicateKeys);

// Debug specific transaction ID (Development function)
router.get('/debug-transaction', walletController.debugTransactionId);

module.exports = router;
