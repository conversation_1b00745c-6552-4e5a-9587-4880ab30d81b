import React, { useState, useEffect } from 'react';
import { Package, Truck, MapPin, Clock, Star, Eye, RefreshCw, Filter } from 'lucide-react';
import { toast } from 'react-toastify';
import { useNavigate } from 'react-router-dom';
import ShippingService from '../api/ShippingService';

const Orders = () => {
  const navigate = useNavigate();
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('buyer');
  const [statusFilter, setStatusFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pagination, setPagination] = useState({
    totalPages: 1,
    totalOrders: 0,
    hasNext: false,
    hasPrev: false
  });

  // Base URL configuration for images
  const baseURL = import.meta.env.VITE_API_BASE_URL;
  const normalizedBaseURL = baseURL.endsWith('/') ? baseURL : `${baseURL}/`;

  const loadOrders = async () => {
    try {
      setLoading(true);
      console.log('🔄 Loading orders with params:', {
        role: activeTab,
        status: statusFilter || null,
        page: currentPage,
        limit: 10
      });

      const response = await ShippingService.getOrders(
        activeTab,
        statusFilter || null,
        currentPage,
        10
      );

      console.log('📦 Orders API response:', response);

      if (response.success) {
        setOrders(response.data.orders || []);
        setPagination(response.data.pagination || {
          totalPages: 1,
          totalOrders: 0,
          hasNext: false,
          hasPrev: false
        });
        console.log('✅ Orders loaded successfully:', response.data.orders?.length || 0, 'orders');

        // Debug: Log first order structure for image debugging
        if (response.data.orders && response.data.orders.length > 0) {
          console.log('🔍 First order structure:', {
            product: response.data.orders[0].product,
            seller: response.data.orders[0].seller,
            buyer: response.data.orders[0].buyer
          });
        }
      } else {
        console.warn('⚠️ Orders API returned success: false');
        toast.error(response.error || 'Failed to load orders');
      }
    } catch (error) {
      console.error('❌ Failed to load orders:', error);
      toast.error(error.error || error.message || 'Failed to load orders');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    console.log('🔄 useEffect triggered - activeTab:', activeTab, 'statusFilter:', statusFilter, 'currentPage:', currentPage);
    loadOrders();
  }, [activeTab, statusFilter, currentPage]);

  const handleStatusUpdate = async (orderId, newStatus) => {
    try {
      const response = await ShippingService.updateOrderStatus(orderId, newStatus);
      if (response.success) {
        toast.success('Order status updated');
        loadOrders();
      }
    } catch (error) {
      console.error('Failed to update order status:', error);
      toast.error(error.error || 'Failed to update order status');
    }
  };

  const handleConfirmDelivery = async (orderId) => {
    try {
      const response = await ShippingService.confirmDelivery(orderId);
      if (response.success) {
        toast.success('Delivery confirmed');
        loadOrders();
      }
    } catch (error) {
      console.error('Failed to confirm delivery:', error);
      toast.error(error.error || 'Failed to confirm delivery');
    }
  };

  const getStatusBadge = (status) => {
    const colorClass = ShippingService.getStatusColor(status);
    return (
      <span className={`px-2 py-1 text-xs rounded-full bg-gray-100 ${colorClass}`}>
        {ShippingService.formatDeliveryStatus(status)}
      </span>
    );
  };

  const getActionButtons = (order) => {
    const isBuyer = activeTab === 'buyer';
    const isSeller = activeTab === 'seller';

    return (
      <div className="flex gap-2">
        <button
          onClick={() => navigate(`/order/${order._id}`)}
          className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
          title="View details"
        >
          <Eye className="w-4 h-4" />
        </button>

        {isSeller && order.status === 'paid' && (
          <button
            onClick={() => handleStatusUpdate(order._id, 'processing')}
            className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Mark Processing
          </button>
        )}

        {isSeller && order.status === 'processing' && (
          <button
            onClick={() => handleStatusUpdate(order._id, 'shipped')}
            className="px-3 py-1 text-xs bg-indigo-600 text-white rounded hover:bg-indigo-700"
          >
            Mark Shipped
          </button>
        )}

        {isBuyer && ['shipped', 'in_transit', 'out_for_delivery'].includes(order.status) && (
          <button
            onClick={() => handleConfirmDelivery(order._id)}
            className="px-3 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700"
          >
            Confirm Delivery
          </button>
        )}
      </div>
    );
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading && orders.length === 0) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Debug Info - Remove in production */}
      {/* {process.env.NODE_ENV === 'development' && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg text-sm">
          <strong>Debug Info:</strong> API URL: <code>http://localhost:5000/api/user/orders?role={activeTab}&page={pagination.currentPage}&limit=10</code>
          {statusFilter && <span> &status={statusFilter}</span>}
          <br />
          <strong>Orders Count:</strong> {orders.length} | <strong>Loading:</strong> {loading ? 'Yes' : 'No'}
        </div>
      )} */}

      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">My Orders</h1>
          <p className="text-gray-600">Track and manage your orders</p>
        </div>
        <button
          onClick={loadOrders}
          className="flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 transition-colors"
          disabled={loading}
        >
          <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </button>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 mb-6">
        {['buyer', 'seller'].map((tab) => (
          <button
            key={tab}
            onClick={() => {
              console.log('🔄 Tab switched to:', tab);
              setActiveTab(tab);
              setCurrentPage(1);
            }}
            className={`px-4 py-2 rounded-md font-medium transition-colors ${
              activeTab === tab
                ? 'bg-teal-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            {tab === 'buyer' ? 'My Purchases' : 'My Sales'}
          </button>
        ))}
      </div>

      {/* Filters */}
      <div className="flex items-center gap-4 mb-6">
        <div className="flex items-center gap-2">
          <Filter className="w-4 h-4 text-gray-500" />
          <select
            value={statusFilter}
            onChange={(e) => {
              console.log('🔍 Status filter changed to:', e.target.value);
              setStatusFilter(e.target.value);
              setCurrentPage(1);
            }}
            className="border border-gray-300 rounded-md px-3 py-1 text-sm"
          >
            <option value="">All Status</option>
            <option value="pending_payment">Pending Payment</option>
            <option value="paid">Paid</option>
            <option value="processing">Processing</option>
            <option value="shipped">Shipped</option>
            <option value="in_transit">In Transit</option>
            <option value="delivered">Delivered</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>
        <div className="text-sm text-gray-500">
          {pagination.totalOrders} total orders
        </div>
      </div>

      {/* Orders List */}
      <div className="space-y-4">
        {orders.length === 0 ? (
          <div className="text-center py-12 bg-white rounded-lg border">
            <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No orders found</h3>
            <p className="text-gray-600">
              {activeTab === 'buyer' 
                ? "You haven't made any purchases yet" 
                : "You haven't made any sales yet"
              }
            </p>
          </div>
        ) : (
          orders.map((order) => (
            <div key={order._id} className="bg-white rounded-lg border shadow-sm p-6">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="font-semibold text-lg">Order #{order.orderNumber}</h3>
                    {getStatusBadge(order.status)}
                    {/* Payment Type Badge */}
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      order.type === 'escrow'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {order.type === 'escrow' ? 'Escrow' : 'Standard'}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600">
                    Placed on {formatDate(order.createdAt)}
                  </p>
                </div>
                {getActionButtons(order)}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Product Info */}
                <div className="flex gap-3">
                  <img
                    src={order?.product?.product_photos?.[0]
                      ? `${normalizedBaseURL}${order.product.product_photos[0]}`
                      : 'https://via.placeholder.com/64x64?text=No+Image'
                    }
                    alt={order?.product?.title || 'Product'}
                    className="w-16 h-16 object-cover rounded-md"
                    onError={(e) => {
                      console.log('Product image failed to load:', e.target.src);
                      e.target.src = 'https://via.placeholder.com/64x64?text=No+Image';
                    }}
                    onLoad={() => {
                      console.log('Product image loaded successfully');
                    }}
                  />
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900 mb-1">
                      {order?.product?.title || 'Product'}
                    </h4>
                    <p className="text-sm text-gray-600">
                      {order?.product?.brand || 'N/A'} • {order?.product?.size || 'N/A'}
                    </p>
                    <p className="text-sm font-medium text-gray-900">
                      ${order?.orderDetails?.offerAmount || order?.orderDetails?.productPrice || '0.00'}
                    </p>
                  </div>
                </div>

                {/* Shipping Info */}
                <div>
                  <h5 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                    <Truck className="w-4 h-4" />
                    Shipping
                  </h5>
                  {order?.shipping?.trackingNumber ? (
                    <div className="space-y-1">
                      <p className="text-sm text-gray-600">
                        Tracking: {order.shipping.trackingNumber}
                      </p>
                      {order?.shipping?.estimatedDelivery && (
                        <p className="text-sm text-gray-600 flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          Est. {formatDate(order.shipping.estimatedDelivery)}
                        </p>
                      )}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500">Not shipped yet</p>
                  )}
                </div>

                {/* Payment Info */}
                <div>
                  <h5 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                    <span className="w-4 h-4 text-green-600">💳</span>
                    Payment Details
                  </h5>
                  <div className="space-y-1">
                    <p className="text-sm text-gray-600">
                      Method: <span className="font-medium">{order.payment?.method || order.type}</span>
                    </p>
                    <p className="text-sm text-gray-600">
                      Gateway: <span className="font-medium">{order.payment?.paymentGateway || 'N/A'}</span>
                    </p>
                    <p className="text-sm text-gray-600">
                      Total: <span className="font-medium text-green-600">
                        ${order.payment?.fees?.total?.toFixed(2) || order.orderDetails?.productPrice?.toFixed(2)}
                      </span>
                    </p>
                    {order.payment?.fees?.platformFee && (
                      <p className="text-sm text-gray-500">
                        Platform Fee: ${order.payment.fees.platformFee.toFixed(2)}
                      </p>
                    )}
                  </div>
                </div>

                {/* Contact Info */}
                {/* <div>
                  <h5 className="font-medium text-gray-900 mb-2">
                    {activeTab === 'buyer' ? 'Seller' : 'Buyer'}
                  </h5>
                  <div className="flex items-center gap-2">
                    <img
                      src={activeTab === 'buyer' &&
                         order?.seller?.profile_picture
                          
                      }
                      alt="Profile"
                      className="w-8 h-8 rounded-full"
                      onError={(e) => {
                        console.log('Profile image failed to load:', e.target.src);
                        // e.target.src = activeTab === 'buyer'
                        //   ? 'https://via.placeholder.com/32x32?text=S'
                        //   : 'https://via.placeholder.com/32x32?text=B';
                      }}
                      onLoad={() => {
                        console.log('Profile image loaded successfully');
                      }}
                    />
                    <span className="text-sm text-gray-900">
                      {activeTab === 'buyer' ? order?.seller?.username || 'Unknown Seller' : order?.buyer?.username || 'Unknown Buyer'}
                    </span>
                  </div>
                </div> */}
              </div>

              {/* Timeline */}
              {order.timeline && order.timeline.length > 0 && (
                <div className="mt-4 pt-4 border-t">
                  <h5 className="font-medium text-gray-900 mb-2">Recent Updates</h5>
                  <div className="space-y-1">
                    {order.timeline.slice(-2).map((event, index) => (
                      <div key={index} className="flex items-center gap-2 text-sm">
                        <div className="w-2 h-2 bg-teal-600 rounded-full"></div>
                        <span className="text-gray-600">
                          {event.description} - {formatDate(event.timestamp)}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))
        )}
      </div>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex justify-center items-center gap-4 mt-8">
          <button
            onClick={() => {
              console.log('📄 Previous page clicked, current page:', currentPage);
              setCurrentPage(prev => prev - 1);
            }}
            disabled={!pagination.hasPrev}
            className="px-4 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            Previous
          </button>
          <span className="text-sm text-gray-600">
            Page {currentPage} of {pagination.totalPages}
          </span>
          <button
            onClick={() => {
              console.log('📄 Next page clicked, current page:', currentPage);
              setCurrentPage(prev => prev + 1);
            }}
            disabled={!pagination.hasNext}
            className="px-4 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            Next
          </button>
        </div>
      )}
    </div>
  );
};

export default Orders;
